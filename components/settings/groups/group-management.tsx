"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { DeleteGroupDialog } from "@/app/app/settings/groups/page";
import { SquarePen } from "@/components/icons/list";

export interface group {
  id: string;
  name: string;
  new_group_name?: string;
}

// group Table Component
export function GroupTable({
  onDelete,
  handleEdit,
  groups,
}: {
  onDelete: (name: string) => void;
  handleEdit: (group: group) => void;
  groups: group[];
}) {
  return (
    <div className="bg-white rounded-lg shadow p-4 overflow-x-auto">
      <div className="w-full overflow-x-auto rounded ">
        <Table className="min-w-max w-full text-xs sm:text-[12px] table-fixed">
          <TableHeader className="bg-gray-200 text-left  text-sm uppercase">
            <TableRow>
              <TableHead className="text-left text-gray-700 dark:text-gray-300 font-semibold text-sm px-6 py-2">
                S.N.
              </TableHead>
              <TableHead className="text-left text-gray-700 dark:text-gray-300 font-semibold text-sm px-6 py-2">
                Group Name
              </TableHead>
              <TableHead className="text-right text-gray-700 dark:text-gray-300 font-semibold text-sm px-6 py-2">
                Actions
              </TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {groups?.map((group, index) => (
              <GroupRow
                key={index}
                group={group}
                index={index}
                handleEdit={handleEdit}
                onDelete={onDelete}
              />
            ))}
          </TableBody>
        </Table>
      </div>
    </div>
  );
}

// group Row Component
function GroupRow({
  group,
  index,
  handleEdit,
  onDelete,
}: {
  group: group;
  index: number;
  handleEdit: (group: group) => void;
  onDelete: (name: string) => void;
}) {
  const router = useRouter();

  return (
    <TableRow>
      <TableCell className="text-left text-sm black px-6 py-2">
        {index + 1}
      </TableCell>
      <TableCell className="text-left text-sm black px-6 py-2">
        {group?.name}
      </TableCell>
      <TableCell className="text-right flex flex-col sm:flex-row items-center justify-end gap-1 sm:gap-2 px-3 py-2 sm:px-6 sm:py-2">
        <Button
          className="bg-green-500 hover:bg-green-600 text-white p-1 rounded-md h-7 w-7"
          onClick={() => {
            handleEdit(group);
          }}
        >
          <SquarePen className="h-4 w-4" />
        </Button>
        <DeleteGroupDialog name={group?.name} onDelete={onDelete} />
      </TableCell>
    </TableRow>
  );
}

// // Add group Form Component
export function AddGroupForm({
  onClose,
  onSubmit,
}: {
  onSubmit: (groupname: group) => void;
  onClose: () => void;
}) {
  const [newgroup, setNewgroup] = useState({
    groupname: "",
  });
  const isformValid = newgroup?.groupname.trim() !== "";

  return (
    <>
      <div className="fixed inset-0 z-50 flex items-center justify-center p-4">
        {" "}
        {/* Added p-4 for padding on small screens */}
        <div
          className="fixed inset-0 bg-black bg-opacity-60"
          // Removed onClick={onClose} from here
        />
        <div className="relative z-10 bg-white p-6 rounded-lg border shadow-sm w-full max-w-md mx-auto overflow-y-auto">
          {" "}
          {/* Added max-w-md and mx-auto for better responsiveness */}
          <div className="mb-8">
            <h1 className="text-3xl font-bold">Add New Group</h1>
            <p className="text-gray-600 mt-2">
              Create a new group with the required information.
            </p>
          </div>
          <form
            onSubmit={(e) => {
              e.preventDefault();
              onSubmit({
                // id: Date.now().toString(), // Ensure id is a string
                groupname: newgroup?.groupname.trim(), // Changed groupname to name to match interface
              });
              setNewgroup({
                groupname: "",
              });
            }}
            className="space-y-4"
          >
            <Input
              placeholder="Group Name"
              value={newgroup?.groupname}
              onChange={(e) =>
                setNewgroup((prev) => ({ ...prev, groupname: e.target.value }))
              }
            />

            <Button type="submit" className="w-full" disabled={!isformValid}>
              Add group
            </Button>
          </form>
          <div className="mt-6 pt-4 border-t">
            <Button variant="outline" onClick={onClose} className="w-full">
              Cancel
            </Button>
          </div>
        </div>
      </div>
    </>
  );
}
