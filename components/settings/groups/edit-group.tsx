"use client";

import { React, useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { group } from "./group-management";

export interface EditGroupFormProps {
  group: group;
  onSubmit: (group: group) => void;
  onClose: () => void;
}

export function EditGroupForm({
  group,
  onSubmit,
  onClose,
}: EditGroupFormProps) {
  const [new_group_name, setNewGroupName] = useState("");

  const isValid = new_group_name.trim() !== "";

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (isValid) {
      const data = { ...group, new_group_name: new_group_name.trim() };
      onSubmit(data);
    }
  };

  return (
    <>
      <div className="fixed inset-0 z-50 flex items-center justify-center p-4 sm:p-6">
        {" "}
        {/* Added padding for responsiveness */}
        <div
          className="fixed inset-0 bg-black bg-opacity-60"
          // Removed onClick handler to prevent closing when clicking overlay
        />
        <div className="relative z-10 w-full max-w-md mx-auto bg-white rounded-lg shadow-lg p-6 sm:p-8">
          {" "}
          {/* Adjusted max-w and added shadow/padding */}
          {/* Header with back button */}
          <div className="mb-8 text-center">
            {" "}
            {/* Centered header text */}
            <h1 className="text-2xl sm:text-3xl font-bold">Edit Group</h1>{" "}
            {/* Responsive font size */}
            <p className="text-gray-600 mt-2 text-sm sm:text-base">
              {" "}
              {/* Responsive font size */}
              Update group information for <strong>{group.name}</strong>.
            </p>
          </div>
          <form onSubmit={handleSubmit} className="space-y-4">
            <div className="space-y-2">
              <label
                htmlFor="groupName"
                className="text-sm font-medium text-gray-700"
              >
                Group Name
              </label>
              <Input
                id="groupName" // Added id for accessibility
                value={new_group_name}
                onChange={(e) => setNewGroupName(e.target.value)}
                placeholder="Enter the Group Name"
                className="w-full" // Ensure input takes full width
              />
            </div>
            <Button type="submit" className="w-full" disabled={!isValid}>
              Save Changes
            </Button>
          </form>
          <div className="mt-6 pt-4 border-t">
            <Button
              variant="custom"
              size="sm"
              onClick={onClose} // This remains the only way to close the form
              className="flex items-center justify-center gap-2 w-full" // Centered cancel button content
            >
              {/* <ArrowLeft className="h-4 w-4" /> */}
              Cancel
            </Button>
          </div>
        </div>
      </div>
    </>
  );
}
