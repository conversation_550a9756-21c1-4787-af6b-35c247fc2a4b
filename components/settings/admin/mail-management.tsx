'use client'
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { useState, React } from 'react';
import { Eye, EyeOff } from "@/components/icons/list";

export interface Mail {
    id: number;
    name: string;
    email: string;
}

export interface MailManagementProps {
    user: any;
    onSubmit: (mail: any) => void;
    onClose: () => void;
}



export function ManageMail({ user, onSubmit, onClose }: MailManagementProps) {
    const [newPassword, setNewPassword] = useState("");
    const [confirmPassword, setConfirmPassword] = useState("");
    const [showPassword, setShowPassword] = useState(false);
    const [showConfirmPassword, setShowConfirmPassword] = useState(false);
    const [isValid, setIsValid] = useState(false);

    const [formData, setFormData] = useState({
        username: user?.username,
        password: "",
        email: user?.email,
    });

    const getChangedFields = () => {
        const changed: Record<string, any> = { id: user?.id };
        for (const key in formData) {
            if (key === "password") {
                if (formData?.password && formData?.password.trim() !== "") {
                    changed.password = formData?.password;
                }
                continue;
            }
            if (formData[key] !== user?.[key]) {
                changed[key] = formData[key];
            }
        }
        return changed;
    }
    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        const changed = getChangedFields();
        onSubmit?.(changed);
    }

    const handleChange = (
        e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>
    ) => {
        const { name, value } = e.target;
        setIsValid(true)
        setFormData((prev) => ({ ...prev, [name]: value }));
        if (name === "password") {
            setShowConfirmPassword(true)
        }// setError(null);
    };

    const handleChangePort = (
        e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>
    ) => {
        const { name, value } = e.target;
        setIsValid(true);
        setFormData((prev) => ({
            ...prev,
            [name]: name === "port" ? Number(value) : value,
        }));
        if (name === "password") {
            setShowConfirmPassword(true);
        }
    };


    return (
        <div className="fixed inset-0 z-50 flex items-center justify-center ">
            <div className="fixed inset-0 bg-black bg-opacity-60" />
            <div className="relative z-10 bg-white p-6 rounded-lg border shadow-sm overflow-y-auto w-[500px]">
                <div className="mb-8">
                    <h1 className="text-3xl font-bold">Edit Mail</h1>
                    <p className="text-gray-600 mt-2">
                        Update user information for{" "}
                        <strong>{user?.username || "Unknown User"}</strong>.
                    </p>
                </div>
                <form onSubmit={handleSubmit} className="space-y-4">
                    {/* <div className="space-y-2 ">
                        <label className="text-sm font-medium text-gray-700">
                            Username
                        </label>
                        <Input
                            value={formData?.username}
                            name="username"
                            onChange={handleChange}
                            placeholder="Username"
                        />
                    </div> */}
                    <div className="space-y-2">
                        <label className="text-sm font-medium text-gray-700">Email</label>
                        <Input
                            value={formData?.email}
                            name="email"
                            onChange={handleChange}
                            placeholder="Email Address"
                        />
                    </div>


                    <div className="space-y-2 ">
                        <label className="text-sm font-medium text-gray-700">
                            New Password
                        </label>
                        <div className="relative ">
                            <Input
                                type={showPassword ? "text" : "password"}
                                name="password"
                                value={formData?.smtphost}
                                onChange={handleChange}
                                placeholder="New Password (leave blank to keep current)"
                            />
                            <Button
                                type="button"
                                variant="ghost"
                                size="sm"
                                className="absolute right-2 top-1/4 -translate-y-1 h-8"
                                onClick={() => setShowPassword(!showPassword)}
                            >
                                {showPassword ? (
                                    <EyeOff className="h-4 w-4" />
                                ) : (
                                    <Eye className="h-4 w-4" />
                                )}
                            </Button>
                        </div>
                    </div>
                    <div className="space-y-2 ">
                        <label className="text-sm font-medium text-gray-700">
                            SMTP HOST
                        </label>
                        <Input
                            value={formData?.smtphost}
                            name="smtphost"
                            onChange={handleChange}
                            placeholder="SMTP Host"
                        />
                    </div>

                    {showConfirmPassword && (
                        <div className="space-y-2">
                            <label className="text-sm font-medium text-gray-700">
                                Confirm New Password
                            </label>
                            <div className="relative">
                                <Input
                                    type={showConfirmPassword ? "text" : "password"}
                                    placeholder="Confirm New Password"
                                    value={confirmPassword}
                                    onChange={(e) => setConfirmPassword(e.target.value)}
                                />
                                <Button
                                    type="button"
                                    variant="ghost"
                                    size="sm"
                                    className="absolute right-2 top-1/4 -translate-y-1 h-8"
                                    onClick={() =>
                                        setConfirmPassword(!showConfirmPassword)}
                                >
                                    {showConfirmPassword ? (
                                        <EyeOff className="h-4 w-4" />
                                    ) : (
                                        <Eye className="h-4 w-4" />
                                    )}
                                </Button>
                            </div>
                        </div>
                    )}
                    <div className="inline-flex  items-center space-x-9">
                        {/* Port input */}
                        <div className="space-y-2">
                            <label className="text-sm font-medium text-gray-700">Port</label>
                            <Input
                                type="number"
                                name="port"
                                min="0"
                                value={formData?.port}
                                onChange={handleChangePort}
                                placeholder="Enter Port"
                                className="appearance-none [&::-webkit-outer-spin-button]:appearance-none [&::-webkit-inner-spin-button]:appearance-none [-moz-appearance:textfield]"

                            />
                        </div>


                        {/* Secure toggle using buttons */}
                        <div className="space-y-2">
                            <label className="text-sm font-medium text-gray-700">Secure</label>
                            <div className="flex items-center">
                                <button
                                    type="button"
                                    onClick={() =>
                                        setFormData((prev) => ({ ...prev, secure: !prev.secure }))
                                    }
                                    className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors focus:outline-none ${formData.secure ? "bg-green-600" : "bg-gray-300"
                                        }`}
                                >
                                    <span
                                        className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${formData.secure ? "translate-x-6" : "translate-x-1"
                                            }`}
                                    />
                                </button>
                                <span className="ml-3 text-sm text-gray-600">
                                    {formData.secure ? "True" : "False"}
                                </span>
                            </div>
                        </div>
                    </div>




                    <Button type="submit" className="w-full" disabled={!isValid}>
                        Save Changes
                    </Button>

                    <Button
                        variant="ghost"
                        // size="sm"
                        onClick={onClose}
                        className="w-full"
                    >
                        Cancel
                    </Button>

                </form>
            </div>
        </div>
    )

}