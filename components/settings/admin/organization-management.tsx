'use client'

import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { useState, React, useEffect } from 'react'
 

export interface OrganizationManagementProps {
  organization: {
    id: number
    name: string
  }
  onSubmit: (org: { id: number; name: string }) => void
  onClose: () => void
}

export function ManageOrganization({
  organization,
  onSubmit,
  onClose,
}: OrganizationManagementProps) {
  const [formData, setFormData] = useState({
    name: organization?.name || '',
  })
  const [isValid, setIsValid] = useState(false)

  useEffect(() => {
    // Enable save only if name is non-empty and changed
    setIsValid(formData.name.trim().length > 0 && formData.name !== organization.name)
  }, [formData.name, organization.name])

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setFormData({ name: e.target.value })
  }

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    onSubmit({ id: organization.id, name: formData.name.trim() })
  }

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center">
      <div className="fixed inset-0 bg-black bg-opacity-60" />
      <div className="relative z-10 bg-white p-6 rounded-lg border shadow-sm w-[400px]">
        <div className="mb-8">
          <h1 className="text-3xl font-bold">Edit Organization</h1>
          <p className="text-gray-600 mt-2">
            Update organization name for <strong>{organization.name || 'Unknown'}</strong>.
          </p>
        </div>

        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-2">
            <label className="text-sm font-medium text-gray-700">Organization Name</label>
            <Input
              value={formData.name}
              name="name"
              onChange={handleChange}
              placeholder="Enter organization name"
            />
          </div>

          <Button type="submit" className="w-full" disabled={!isValid}>
            Save Changes
          </Button>

          <Button variant="ghost" onClick={onClose} className="w-full">
            Cancel
          </Button>
        </form>
      </div>
    </div>
  )
}
