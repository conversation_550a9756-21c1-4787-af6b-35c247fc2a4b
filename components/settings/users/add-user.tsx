"use client";

import React, { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { useAuth } from "@/context/AuthContext";
import { useFetch } from "@/hooks/useFetchOnMount";
import { Eye, EyeOff } from "@/components/icons/list";

export type UserGroup = string;
export type UserPackage = string;

export interface Group {
  id: number;
  name: string;
}

export interface Package {
  id: number;
  package_name: string;
}

export interface User {
  id: number;
  username: string;
  email: string;
  group: UserGroup;
  password: string;
  package_: UserPackage;
  status: string;
  organization: string;
}
interface AddUserProps {
  onSubmit: (user: any) => void;
  onClose: () => void;
}

export function AddUserForm({ onSubmit, onClose }: AddUserProps) {
  const [newUser, setNewUser] = useState({
    username: "",
    password: "",
    confirmPassword: "",
    email: "",
    package_: "" as UserPackage,
    group: "" as UserGroup,
    status: "active",
  }); // const access_token = Cookies.get("accessToken");

  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);

  // const [loadingGroups, setLoadingGroups] = useState(true);

  const [selectedGroupId, setSelectedGroupId] = useState("");
  const [selectedPackageId, setSelectedPackageId] = useState("");
  const { org_id } = useAuth();
  const { data: group, loading, error } = useFetch("/groups");
  let groups: Group[] = group;

  const handlePasswordChange = (password: string) => {
    setNewUser((prev) => ({ ...prev, password }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!isValid) {
      // Potentially show a more general error or highlight invalid fields
      return;
    }
    try {
      const payload = {
        username: newUser?.username.trim(),
        password: newUser?.password.trim(),
        email: newUser?.email.trim(),
        status: newUser?.status.toLowerCase(),
        org_id: org_id?.trim(), // Ensure org_id is trimmed and optionally chained
        group_id: selectedGroupId.trim(),
        // Only include package_id if a package is selected
        ...(selectedPackageId && { package_id: selectedPackageId.trim() }),
      };
      onSubmit?.(payload);
    } catch (error) {
      console.error("Failed to add user:", error);
      alert("Failed to add user"); // Consider a more user-friendly notification
    }
  };

  // Improved isValid logic for real-time validation feedback
  const isValid =
    newUser?.username.trim() !== "" &&
    newUser?.email.trim() !== "" &&
    newUser?.email.match(/^[^\s@]+@[^\s@]+\.[^\s@]+$/) &&
    selectedGroupId.trim() !== "" &&
    newUser?.password.trim() !== "" && // Password cannot be empty
    newUser?.confirmPassword.trim() !== "" && // Confirm password cannot be empty
    newUser?.password === newUser?.confirmPassword; // Passwords must match

  return (
    <>
      <div className="fixed inset-0 z-50 flex items-center justify-center ">
        <div className="fixed inset-0 bg-black bg-opacity-60 backdrop-blur-sm" />
        <div className="relative bg-white rounded-xl shadow-2xl overflow-hidden max-w-md md:max-w-lg lg:max-w-xl xl:max-w-2xl w-full max-h-[90vh] flex flex-col">
          {/* Page title */}
          <div className="px-5 py-3 bg-gradient-to-r bg-primary text-white border-b ">
            <h1 className="text-lg font-extrabold text-center">Add New User</h1>{" "}
            <p className="text-blue-100 text-sm  text-center">
              Create a new user account and assign them to a group.
            </p>
          </div>
          <form
            onSubmit={handleSubmit}
            className="flex-grow p-6 grid grid-cols-1 md:grid-cols-2 gap-x-6 gap-y-5 overflow-y-auto custom-scrollbar"
          >
            <div>
              <label
                htmlFor="username"
                className="block text-sm font-semibold text-gray-700 "
              >
                Username
              </label>
              <Input
                placeholder="Username"
                value={newUser?.username}
                onChange={(e) =>
                  setNewUser((prev) => ({ ...prev, username: e.target.value }))
                }
              />
            </div>

            <div className="relative">
              <label
                htmlFor="password"
                className="block text-sm font-semibold text-gray-700 "
              >
                Password
              </label>
              <Input
                type={showPassword ? "text" : "password"}
                placeholder="Enter password"
                value={newUser?.password}
                onChange={(e) => handlePasswordChange(e.target.value)}
              />
              <Button
                type="button"
                variant="ghost"
                size="sm"
                className="absolute right-2.5 top-1/2 -translate-y-1.5 h-8 px-2 py-1 text-gray-500 hover:bg-gray-200 rounded-md"
                onClick={() => setShowPassword(!showPassword)}
              >
                {showPassword ? (
                  <EyeOff className="h-4 w-4" />
                ) : (
                  <Eye className="h-4 w-4" />
                )}
              </Button>
            </div>

            <div className="relative">
              <label
                htmlFor="confirmPassword"
                className="block text-sm font-semibold text-gray-700"
              >
                Confirm Password
              </label>
              <Input
                type={showConfirmPassword ? "text" : "password"}
                placeholder="Confirm Password"
                value={newUser?.confirmPassword}
                onChange={(e) =>
                  setNewUser((prev) => ({
                    ...prev,
                    confirmPassword: e.target.value,
                  }))
                }
              />
              <Button
                type="button"
                variant="ghost"
                size="sm"
                className="absolute right-2.5 top-1/2 -translate-y-1.5 h-8 px-2 py-1 text-gray-500 hover:bg-gray-200 rounded-md"
                onClick={() => setShowConfirmPassword(!showConfirmPassword)}
              >
                {showConfirmPassword ? (
                  <EyeOff className="h-4 w-4" />
                ) : (
                  <Eye className="h-4 w-4" />
                )}
              </Button>
              {newUser.password &&
                newUser.confirmPassword &&
                newUser.password !== newUser.confirmPassword && (
                  <p className="text-red-500 text-xs mt-1">
                    Passwords do not match.
                  </p>
                )}
            </div>

            <div>
              <label
                htmlFor="email"
                className="block text-sm font-semibold text-gray-700"
              >
                Email Address
              </label>
              <Input
                id="email"
                type="email"
                placeholder="e.g. <EMAIL>"
                value={newUser?.email}
                pattern="[a-z0-9._%+-]+@[a-z0-9.-]+\.[a-z]{2,}$"
                required
                onChange={(e) =>
                  setNewUser((prev) => ({ ...prev, email: e.target.value }))
                }
              />
              {newUser?.email &&
                !newUser?.email.match(/^[^\s@]+@[^\s@]+\.[^\s@]+$/) && (
                  <span className="text-sm text-red-500 mt-1 block">
                    Please enter a valid email address.
                  </span>
                )}
            </div>
            {/* <PackageSelect
              selectedPackageId={selectedPackageId}
              setSelectedPackageId={setSelectedPackageId}
              packages={packages}
              loading={loadingPackages}
            /> */}

            {/* Group Select */}
            <GroupSelect
              selectedGroupId={selectedGroupId}
              setSelectedGroupId={setSelectedGroupId}
              groups={groups}
              loading={loading}
            />

            <div>
              <label
                htmlFor="status"
                className="block text-sm font-semibold text-gray-700"
              >
                Status
              </label>
              <select
                id="status"
                className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent transition duration-200 ease-in-out text-gray-800"
                value={newUser?.status}
                onChange={(e) =>
                  setNewUser((prev) => ({ ...prev, status: e.target.value }))
                }
                required
              >
                <option value="active">Active</option>
                <option value="inactive">Inactive</option>
              </select>
            </div>

            <div className="md:col-span-2 flex justify-end items-center gap-4 pt-4 border-t border-gray-200 mt-4">
              <Button
                size="sm"
                type="button"
                variant="custom"
                onClick={onClose}
                className="text-gray-700 hover:text-gray-900 px-4 py-2 rounded-md"
              >
                Cancel
              </Button>
              <Button
                size="sm"
                type="submit"
                className="text-white px-6 py-2.5 rounded-md"
                disabled={!isValid}
              >
                Create User
              </Button>
            </div>
          </form>
        </div>
      </div>
    </>
  );
}
function GroupSelect({
  // value,
  selectedGroupId,
  setSelectedGroupId,
  groups,
  loading,
}: {
  // value: UserGroup;
  selectedGroupId: string;
  setSelectedGroupId: (group: UserGroup) => void;
  groups: Group[];
  loading: boolean;
}) {
  return (
    <div>
      <label
        htmlFor="group"
        className="block text-sm font-semibold text-gray-700"
      >
        Group
      </label>
      <select
        className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent transition duration-200 ease-in-out text-gray-800"
        value={selectedGroupId}
        onChange={(e) => setSelectedGroupId(e.target.value)}
        required
      >
        <option value="">Select a group</option>
        {groups?.map((group) => (
          <option key={group?.id} value={group?.id.toString()}>
            {group?.name}
          </option>
        ))}
      </select>
    </div>
  );
}

function PackageSelect({
  selectedPackageId,
  setSelectedPackageId,
  packages,
  loading,
}: {
  selectedPackageId: string;
  setSelectedPackageId: (package_: UserPackage) => void;
  packages: Package[];
  loading: boolean;
}) {
  return (
    <div className="space-y-1">
      <label className="text-xs font-medium text-gray-700">Department</label>
      <select
        className="w-full p-2 border rounded-md"
        value={selectedPackageId}
        onChange={(e) => setSelectedPackageId(e.target.value)}
        required
      >
        <option value="">Select a department</option>
        {packages?.map((package_) => (
          <option key={package_?.id} value={package_?.id.toString()}>
            {package_?.package_name}
          </option>
        ))}
      </select>
    </div>
  );
}
