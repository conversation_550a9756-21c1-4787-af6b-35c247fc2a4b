import React from "react";

interface CustomerHeaderProps {
  firstName: string;
  lastName: string;
  status: string;
  department: string;
}

const CustomerHeader: React.FC<CustomerHeaderProps> = ({
  firstName,
  lastName,
  status,
  department,
}) => {
  return (
    <div className="grid grid-cols-1 md:grid-cols-3 items-center p-2 rounded-lg text-sm gap-2">
      <div className="font-semibold text-center md:text-left">
        {firstName} {lastName}
      </div>
      <div className="text-center">
        Status:{" "}
        <span
          className={`font-medium ${
            status === "Active"
              ? "text-green-600"
              : status === "Inactive"
              ? "text-yellow-600"
              : "text-red-600"
          }`}
        >
          {status}
        </span>
      </div>
      <div className="text-center md:text-right text-gray-700 flex flex-col md:flex-row items-center md:justify-end gap-2">
        <span>Department: {department}</span>
      </div>
    </div>
  );
};

export default CustomerHeader;
