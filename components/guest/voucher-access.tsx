"use client";

import React, { useState, useEffect } from "react";
import { Voucher, VoucherFormData } from "@/types/interface-type";
import apiClient from "@/lib/apiClient";
import { Button } from "../ui/button";
import { Input } from "../ui/input";
import { Label } from "../ui/label";
import { Textarea } from "../ui/textarea";
import { toast } from "sonner";
import { ArrowLeft, ChevronLeft, ChevronRight } from "@/components/icons/list";

type CopyButtonProps = {
  text: string;
  className?: string;
};

export default function GuestVoucherAccessPage() {
  const [vouchers, setVouchers] = useState<Voucher[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedVoucher, setSelectedVoucher] = useState<Voucher | null>(null);
  const [showForm, setShowForm] = useState(false);
  const [search, setSearch] = useState("");
  const [currentPage, setCurrentPage] = useState(1);
  const [selectedItemsPerPageValue, setSelectedItemsPerPageValue] =
    useState<string>("15");

  useEffect(() => {
    if (!selectedVoucher) return;

    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === "Escape") {
        setSelectedVoucher(null);
      }
    };

    document.addEventListener("keydown", handleKeyDown);
    return () => document.removeEventListener("keydown", handleKeyDown);
  }, [selectedVoucher]);

  const fetchVouchers = async () => {
    try {
      const res = await apiClient.get("/guest/voucher/?type=voucher");
      // Backend returns: { success: true, data: [...] }
      // The apiClient response interceptor extracts the data property
      setVouchers(res?.data?.data || []);
    } catch (err) {
      console.error("Error fetching vouchers:", err);
      toast.error(err.message || "Failed to fetch vouchers");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchVouchers();
  }, []);

  if (loading) return <div className="p-4">Loading vouchers...</div>;

  const createVoucher = async (data: VoucherFormData) => {
    try {
      await apiClient.post("/guest/voucher", data);
      // Backend returns: { success: true, message: "Voucher has been created" }
      toast.success("Voucher was successfully created!!");
      fetchVouchers();
    } catch (err) {
      console.error("Error creating voucher:", err);
      toast.error(err.message || "Failed to create voucher");
      fetchVouchers();
    }
  };

  // Filter vouchers based on search term and sort by name in ascending order
  const filteredVouchers = vouchers
    .filter(
      (voucher: Voucher) =>
        voucher?.data?.name?.toLowerCase().includes(search.toLowerCase()) ||
        voucher?.data?.email?.toLowerCase().includes(search.toLowerCase()) ||
        voucher?.data?.company?.toLowerCase().includes(search.toLowerCase())
    )
    .sort((a: Voucher, b: Voucher) => {
      const dateA = new Date(a?.created_at || "");
      const dateB = new Date(b?.created_at || "");
      return dateB.getTime() - dateA.getTime(); // newest first
    });

  // Pagination logic
  const itemsPerPage =
    selectedItemsPerPageValue === "all"
      ? filteredVouchers.length
      : parseInt(selectedItemsPerPageValue, 10);

  const totalPages =
    itemsPerPage === filteredVouchers.length
      ? 1
      : Math.ceil(filteredVouchers.length / itemsPerPage);

  const currentVouchers = filteredVouchers.slice(
    (currentPage - 1) * itemsPerPage,
    currentPage * itemsPerPage
  );

  // Handle items per page change
  const handleItemsPerPageChange = (
    e: React.ChangeEvent<HTMLSelectElement>
  ) => {
    const value = e.target.value;
    setSelectedItemsPerPageValue(value);
    setCurrentPage(1);
  };

  return (
    <div>
      <div className="flex flex-col sm:flex-row justify-between items-center sm:items-center flex-wrap gap-4 mb-3">
        <div className="flex items-center gap-2 text-xs">
          <span>Show</span>
          <select
            value={selectedItemsPerPageValue}
            onChange={handleItemsPerPageChange}
            className="border border-gray-300 rounded-md p-1.5 h-7 text-xs focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="15">15</option>
            <option value="30">30</option>
            <option value="50">50</option>
            <option value="100">100</option>
            <option value="all">All</option>
          </select>
          <span>entries</span>
        </div>

        {/* Right side - Search and Add button */}
        <div className="flex flex-col sm:flex-row items-stretch sm:items-center gap-3 w-full sm:w-auto ">
          <Input
            type="text"
            placeholder="Search Vouchers"
            value={search}
            onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
              setSearch(e.target.value)
            }
            className="w-full sm:w-[320px] p-1 border border-gray-300  px-3 focus:ring focus:border-blue-300 rounded-full"
          />
          <Button
            onClick={() => setShowForm(true)}
            className="w-full sm:w-auto rounded-full"
          >
            Generate Voucher
          </Button>
        </div>

        {showForm && (
          <VoucherForm
            onSubmit={(data) => {
              createVoucher(data);
              setShowForm(false);
            }}
            onCancel={() => setShowForm(false)}
          />
        )}
      </div>

      <div className="w-full overflow-x-auto shadow-sm">
        <table className="min-w-max w-full text-xs">
          <thead className="bg-gray-200 text-left uppercase align-middle">
            <tr>
              <th className="border-b border-gray-300 px-1 sm:px-2 py-1">
                Name
              </th>
              <th className="border-b border-gray-300 px-1 sm:px-2 py-2 hidden sm:table-cell">
                Email
              </th>
              <th className="border-b border-gray-300 px-1 sm:px-2 py-2 hidden md:table-cell">
                Company
              </th>
              <th className="border-b border-gray-300 px-1 sm:px-2 py-2 hidden md:table-cell">
                Created At
              </th>
              <th className="border-b border-gray-300 px-1 sm:px-2 py-2 text-center">
                Status
              </th>
            </tr>
          </thead>
          <tbody>
            {currentVouchers.length === 0 ? (
              <tr>
                <td
                  colSpan={5}
                  className="px-1 sm:px-2 py-8 text-center text-blue-900 text-sm"
                >
                  Oops! No vouchers matched your search
                </td>
              </tr>
            ) : (
              currentVouchers?.map((voucher: Voucher) => (
                <tr
                  key={voucher?.id}
                  className="hover:bg-gray-50 cursor-pointer"
                  onClick={() => setSelectedVoucher(voucher)}
                >
                  <td className="border-b border-gray-300 px-1 sm:px-2 py-2 text-blue-600 font-medium cursor-pointer">
                    {voucher?.data?.name}
                  </td>
                  <td className="border-b border-gray-300 px-1 sm:px-2 py-2 hidden sm:table-cell">
                    {voucher?.data?.email}
                  </td>
                  <td className="border-b border-gray-300 px-1 sm:px-2 py-2 hidden md:table-cell">
                    {voucher?.data?.company}
                  </td>
                  <td className="border-b border-gray-300 px-1 sm:px-2 py-2 hidden md:table-cell">
                    {new Date(voucher?.created_at).toLocaleString()}
                  </td>
                  <td className="border-b border-gray-300 px-1 sm:px-2 py-2 text-center">
                    <span
                      className={`px-2 py-1 rounded-full text-xs ${
                        voucher?.status.toLowerCase() === "accept"
                          ? "bg-green-300 text-green-800"
                          : "bg-yellow-100 text-yellow-800"
                      }`}
                    >
                      {voucher?.status.toUpperCase()}
                    </span>
                  </td>
                </tr>
              ))
            )}
          </tbody>
        </table>
      </div>

      {totalPages > 1 && (
        <div className="flex items-center gap-1 justify-left mt-4">
          <Button
            className="rounded-full w-8 h-6"
            size="sm"
            onClick={() => setCurrentPage((p: number) => Math.max(p - 1, 1))}
            disabled={currentPage === 1}
          >
            <ChevronLeft />
          </Button>
          <span className="text-xs">
            Page {currentPage} of {totalPages}
          </span>
          <Button
            className="rounded-full w-8 h-6"
            size="sm"
            onClick={() =>
              setCurrentPage((p: number) => Math.min(p + 1, totalPages))
            }
            disabled={currentPage === totalPages}
          >
            <ChevronRight />
          </Button>
        </div>
      )}

      {/* Voucher Access Details Modal */}
      {selectedVoucher && (
        <div className="fixed inset-0 z-50 flex items-center justify-center">
          <div className="fixed inset-0 bg-black bg-opacity-60" />
          <div className="relative bg-white p-5 rounded-lg w-full max-w-md md:max-w-lg lg:max-w-xl xl:max-w-2xl shadow-lg overflow-y-auto max-h-[90vh]">
            <div className="mb-5">
              <h1 className="text-lg font-bold">Voucher Access Details</h1>
              <p className="text-gray-800 text-sm">
                Detailed information for voucher access of{" "}
                <strong className="text-blue-500">
                  {selectedVoucher?.data?.name || "Unknown User"}
                </strong>
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
              <DetailField label="Name" value={selectedVoucher?.data.name} />
              <DetailField
                label="Email or Mobile"
                value={selectedVoucher?.data.email}
              />
              <DetailField
                label="Code"
                value={
                  <div className="flex items-center justify-between w-full">
                    <span>{selectedVoucher?.data.code}</span>
                    <CopyButton
                      text={selectedVoucher?.data.code}
                      className="text-xs"
                    />
                  </div>
                }
              />

              <DetailField
                label="Company"
                value={selectedVoucher?.data.company}
              />
              <DetailField
                label="Purpose"
                value={selectedVoucher?.data.purpose}
              />
              <DetailField
                label="Status"
                value={
                  <span
                    className={`inline-block px-2 py-0.5 rounded text-xs font-medium ${
                      selectedVoucher?.status.toLowerCase() === "accept"
                        ? "bg-green-400 text-green-800"
                        : "bg-yellow-400 text-yellow-900"
                    }`}
                  >
                    {selectedVoucher?.status.toUpperCase()}
                  </span>
                }
              />
              <DetailField
                label="Created At"
                value={new Date(selectedVoucher?.created_at).toLocaleString()}
              />
            </div>

            <div className="mt-5">
              <Button
                onClick={() => setSelectedVoucher(null)}
                className="w-auto px-3 rounded-full"
              >
                <ArrowLeft className="w-4 h-4" />
                Back to List
              </Button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}

function DetailField({
  label,
  value,
}: {
  label: string;
  value: React.ReactNode;
}) {
  return (
    <div className="space-y-1.5">
      <label className="text-xs font-medium text-gray-700">{label}</label>
      <div className="w-full p-1.5 border rounded-md bg-gray-50 text-gray-900">
        {value || "N/A"}
      </div>
    </div>
  );
}

function VoucherForm({
  initialData,
  onSubmit,
  onCancel,
  isEdit = false,
}: {
  initialData?: VoucherFormData;
  onSubmit: (data: VoucherFormData) => void;
  onCancel?: () => void;
  isEdit?: boolean;
}) {
  const [formData, setFormData] = useState<VoucherFormData>({
    name: initialData?.name || "",
    email: initialData?.email || "",
    mobile: initialData?.mobile || "",
    company: initialData?.company || "",
    purpose: initialData?.purpose || "",
    session: initialData?.session || "",
  });

  const [errors, setErrors] = useState<Partial<VoucherFormData>>({});

  const handleChange = (field: keyof VoucherFormData, value: string) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
    if (errors[field]) {
      setErrors((prev) => ({ ...prev, [field]: "" }));
    }
  };

  const validateForm = (): boolean => {
    const newErrors: Partial<VoucherFormData> = {};
    if (!formData?.name.trim()) newErrors.name = "Name is required";
    if (!formData?.email.trim()) newErrors.email = "Email is required";
    if (!formData?.mobile.trim()) newErrors.mobile = "Mobile is required";
    if (!formData?.company.trim()) newErrors.company = "Company is required";
    if (!formData?.purpose.trim()) newErrors.purpose = "Purpose is required";

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (validateForm()) {
      onSubmit(formData);
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-60 flex items-center justify-center p-4 z-100">
      {" "}
      <div className="bg-white p-5 sm:p-5 rounded-lg w-full max-w-md md:max-w-lg lg:max-w-xl xl:max-w-2xl shadow-lg overflow-y-auto max-h-[90vh]">
        {" "}
        <div className="text-lg sm:text-lg font-bold text-black tracking-wide mb-3">
          {" "}
          Generate New Voucher
        </div>
        <form onSubmit={handleSubmit} className="space-y-3 sm:space-y-3">
          {" "}
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
            {" "}
            <div>
              <Label htmlFor="name">Name *</Label>
              <Input
                id="name"
                type="text"
                value={formData.name}
                onChange={(e) => handleChange("name", e.target.value)}
                placeholder="Enter name"
                className={errors.name ? "border-red-500" : ""}
              />
              {errors.name && (
                <p className="text-sm text-red-500">{errors.name}</p>
              )}
            </div>
            <div>
              <Label htmlFor="email">Email *</Label>
              <Input
                id="email"
                type="email"
                value={formData.email}
                onChange={(e) => handleChange("email", e.target.value)}
                placeholder="Enter email"
                className={errors.email ? "border-red-500" : ""}
              />
              {errors.email && (
                <p className="text-sm text-red-500">{errors.email}</p>
              )}
            </div>
            <div>
              <Label htmlFor="mobile">Mobile *</Label>
              <Input
                id="mobile"
                type="tel"
                value={formData.mobile}
                onChange={(e) => handleChange("mobile", e.target.value)}
                placeholder="Enter mobile"
                className={errors.mobile ? "border-red-500" : ""}
              />
              {errors.mobile && (
                <p className="text-sm text-red-500">{errors.mobile}</p>
              )}
            </div>
            <div>
              <Label htmlFor="company">Company *</Label>
              <Input
                id="company"
                type="text"
                value={formData.company}
                onChange={(e) => handleChange("company", e.target.value)}
                placeholder="Enter company"
                className={errors.company ? "border-red-500" : ""}
              />
              {errors.company && (
                <p className="text-sm text-red-500">{errors.company}</p>
              )}
            </div>
            <div className="sm:col-span-2">
              {" "}
              <Label htmlFor="purpose">Purpose *</Label>
              <Textarea
                id="purpose"
                value={formData.purpose}
                onChange={(e) => handleChange("purpose", e.target.value)}
                rows={3}
                placeholder="Enter purpose"
                className={errors.purpose ? "border-red-500" : ""}
              />
              {errors.purpose && (
                <p className="text-sm text-red-500">{errors.purpose}</p>
              )}
            </div>
            <div className="sm:col-span-2">
              {" "}
              <Label htmlFor="session">Session (Hours) *</Label>
              <Input
                id="session"
                type="number"
                min={1}
                value={formData.session}
                onChange={(e) => handleChange("session", e.target.value)}
                placeholder="Enter session"
                className={errors.session ? "border-red-500" : ""}
              />
              {errors.session && (
                <p className="text-sm text-red-500">{errors.session}</p>
              )}
            </div>
          </div>
          <div className="flex flex-col sm:flex-row gap-3 pt-4">
            {" "}
            <Button type="submit" className="flex-1">
              {isEdit ? "Update Voucher" : "Create Voucher"}
            </Button>
            {onCancel && (
              <Button
                type="button"
                variant="outline"
                onClick={onCancel}
                className="flex-1"
              >
                Cancel
              </Button>
            )}
          </div>
        </form>
      </div>
    </div>
  );
}

function CopyButton({ text, className }: CopyButtonProps) {
  const [copied, setCopied] = useState(false);

  const handleCopy = async () => {
    try {
      await navigator.clipboard.writeText(text);
      setCopied(true);
      setTimeout(() => setCopied(false), 1500);
    } catch (err) {
      console.error("Copy failed", err);
    }
  };

  return (
    <button
      onClick={handleCopy}
      className={`px-2 py-0.5 bg-gray-200 hover:bg-gray-300 rounded ${
        className || ""
      }`}
    >
      {copied ? "Copied!" : "Copy"}
    </button>
  );
}
