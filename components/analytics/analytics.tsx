"use client";
import React, { useState, useEffect, useMemo } from "react";
import { toast } from "sonner";
import apiClient from "@/lib/apiClient";
import { But<PERSON> } from "../ui/button";
import { ChevronLeft, ChevronRight } from "@/components/icons/list";
import { useSearchParams, useRouter } from "next/navigation";
import { DepartmentStats, PackageInfo } from "@/types/interface-type";
import {
  BarChart,
  LineChart,
  Line,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
} from "recharts";

// Simplified interfaces
interface BarChartData {
  name: string;
  download: number;
  upload: number;
}

export interface LineChartData {
  timestamp: string;
  totalUsage: number;
}

interface ActiveStat extends DepartmentStats {
  inputMbps: number;
  outputMbps: number;
  count: number;
  recordId: string;
  created_at: string;
}

// Utility functions
const formatBytesToGB = (bytes: string | number) => (Number(bytes) / (1024 * 1024 * 1024)).toFixed(2);

const getDefaultDates = () => {
  const yesterday = new Date();
  yesterday.setDate(yesterday.getDate() - 1);
  const today = new Date();
  return {
    start: yesterday.toISOString().split("T")[0],
    end: today.toISOString().split("T")[0]
  };
};

// Simplified chart components
const AnalyticsBarChart: React.FC<{ data: BarChartData[] }> = ({ data }) => {
  if (!data?.length) {
    return (
      <div className="flex items-center justify-center h-full">
        <p className="text-gray-500">No data to display</p>
      </div>
    );
  }

  const dynamicBarSize = Math.max(10, 200 / data.length);

  return (
    <ResponsiveContainer width="100%" height="100%">
      <h1 className="text-center">Top 5 Users based on Usage</h1>
      <BarChart data={data} layout="vertical" margin={{ top: 1, right: 20, left: 20, bottom: 10 }}>
        <CartesianGrid strokeDasharray="0 0" />
        <YAxis dataKey="name" type="category" />
        <XAxis type="number" />
        <Tooltip />
        <Legend verticalAlign="top" align="right" layout="horizontal" />
        <Bar dataKey="download" fill="#8884d8" radius={[0, 30, 30, 0]} barSize={dynamicBarSize} />
        <Bar dataKey="upload" fill="#82ca9d" position="inside" barSize={dynamicBarSize} radius={[0, 30, 30, 0]} />
      </BarChart>
    </ResponsiveContainer>
  );
};

const AnalyticsLineChart: React.FC<{ data: LineChartData[] }> = ({ data }) => {
  if (!data?.length) {
    return <p className="text-gray-500 text-center">No line chart data</p>;
  }

  return (
    <ResponsiveContainer width="100%" height="100%">
      <LineChart data={data} margin={{ top: 5, right: 20, left: 20, bottom: 5 }}>
        <CartesianGrid strokeDasharray="3 3" />
        <XAxis
          dataKey="timestamp"
          domain={['dataMin', 'dataMax']}
          tickFormatter={(v: any) => new Date(v).toLocaleTimeString([], { hour: "2-digit", minute: "2-digit" })}
        />
        <YAxis />
        <Tooltip />
        <Legend verticalAlign="top" align="right" layout="horizontal" />
        <Line
          type="monotone"
          dataKey="totalUsage"
          stroke="#ff7300"
          strokeWidth={2}
          dot={false}
          activeDot={{ r: 4 }}
        />
      </LineChart>
    </ResponsiveContainer>
  );
};

export default function AnalyticsPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const defaultDates = getDefaultDates();

  // Simplified state management
  const [departments, setDepartments] = useState<PackageInfo[]>([]);
  const [selectedDepartment, setSelectedDepartment] = useState<string>("");
  const [aggregatedStats, setAggregatedStats] = useState<DepartmentStats[]>([]);
  const [individualStats, setIndividualStats] = useState<DepartmentStats[]>([]);
  const [activeStats, setActiveStats] = useState<ActiveStat[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [startDate, setStartDate] = useState(defaultDates.start);
  const [endDate, setEndDate] = useState(defaultDates.end);
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState<string>("15");
  const [lineChartData, setLineChartData] = useState<LineChartData[]>([]);

  // Modal state
  const [selectedUser, setSelectedUser] = useState<string | null>(null);
  const [userSessions, setUserSessions] = useState<DepartmentStats[]>([]);
  const [modalCurrentPage, setModalCurrentPage] = useState(1);
  const [modalItemsPerPage, setModalItemsPerPage] = useState(10);
  const [modalSearch, setModalSearch] = useState("");

  // Get active view from URL params
  const getActiveView = (): "aggregated" | "individual" | "active" => {
    const typeParam = searchParams.get("type");
    if (typeParam === "individualview") return "individual";
    if (typeParam === "activesessions") return "active";
    return "aggregated";
  };

  const [activeView, setActiveView] = useState<"aggregated" | "individual" | "active">(getActiveView);

  // Simplified event handlers
  const handleDepartmentChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    setSelectedDepartment(e.target.value);
  };

  const handleItemsPerPageChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    setItemsPerPage(e.target.value);
    setCurrentPage(1);
  };

  const handleViewChange = (view: "aggregated" | "individual" | "active") => {
    setActiveView(view);
    setCurrentPage(1);

    const params = new URLSearchParams(searchParams.toString());
    const viewTypes = { individual: "individualview", active: "activesessions", aggregated: "aggregatedview" };
    params.set("type", viewTypes[view]);
    router.replace(`?${params.toString()}`);
  };

  const handleUsernameClick = (username: string) => {
    const sessions = individualStats.filter((session: DepartmentStats) => session.username === username);
    setUserSessions(sessions);
    setSelectedUser(username);
    setModalCurrentPage(1);
  };

  const closeUserSessionsModal = () => {
    setSelectedUser(null);
    setModalCurrentPage(1);
    setModalSearch("");
  };

  // Simplified data processing functions
  const aggregateUserData = (rawData: DepartmentStats[]): DepartmentStats[] => {
    const userMap = new Map<string, DepartmentStats>();

    rawData.forEach((curr: DepartmentStats) => {
      const existing = userMap.get(curr.username);
      if (existing) {
        existing.acctinputoctets = (Number(existing.acctinputoctets) + Number(curr.acctinputoctets)).toString();
        existing.acctoutputoctets = (Number(existing.acctoutputoctets) + Number(curr.acctoutputoctets)).toString();
      } else {
        userMap.set(curr.username, { ...curr });
      }
    });

    return Array.from(userMap.values());
  };

  const processLineChartData = (rawData: DepartmentStats[]): LineChartData[] => {
    const hourlyUsageMap = new Map<string, number>();

    rawData.forEach(item => {
      const startTime = new Date(item.acctstarttime);
      startTime.setMinutes(0, 0, 0);
      const timestampKey = startTime.toISOString();

      const totalUsageMB = (Number(item.acctinputoctets) + Number(item.acctoutputoctets)) / 1024 / 1024;
      hourlyUsageMap.set(timestampKey, (hourlyUsageMap.get(timestampKey) || 0) + totalUsageMB);
    });

    return Array.from(hourlyUsageMap.entries())
      .map(([timestamp, totalUsage]) => ({
        timestamp,
        totalUsage: parseFloat(totalUsage.toFixed(2))
      }))
      .sort((a, b) => new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime());
  };

  const fetchDepartmentStats = async () => {
    setLoading(true);
    const payload = {
      group: selectedDepartment ? [selectedDepartment] : departments.map((d: PackageInfo) => d.package_name),
      start: `${startDate} 00:00:00`,
      end: `${endDate} 00:00:00`,
    };

    try {
      const res = await apiClient.post("/department/stats", payload);
      const rawData = res?.data || [];

      setIndividualStats(rawData);
      setAggregatedStats(aggregateUserData(rawData));
      setLineChartData(processLineChartData(rawData));
    } catch (err) {
      toast.error("Error fetching department stats");
    } finally {
      setLoading(false);
    }
  };

  const fetchActiveSessionsData = async () => {
    setLoading(true);
    try {
      const payload = {
        group: departments.map((d: PackageInfo) => d.package_name),
        limit: 1,
      };

      const res = await apiClient.post("/analytics", payload);
      const analyticsData = res?.data || [];

      if (analyticsData.length > 0) {
        const latestRecord = analyticsData[0];
        const activeUsers = latestRecord.data.map((user: any) => ({
          username: user.username,
          acctstarttime: user.acctstarttime,
          acctinputoctets: user.acctinputoctets,
          acctoutputoctets: user.acctoutputoctets,
          inputMbps: latestRecord.inputMbps || 0,
          outputMbps: latestRecord.outputMbps || 0,
          count: latestRecord.count,
          recordId: latestRecord.id,
          created_at: latestRecord.created_at,
        }));
        setActiveStats(activeUsers);
      } else {
        setActiveStats([]);
      }
    } catch (err) {
      console.error("Error fetching active sessions:", err);
      toast.error("Error fetching active sessions");
      setActiveStats([]);
    } finally {
      setLoading(false);
    }
  };

  const fetchDepartments = async () => {
    try {
      const res = await apiClient.get("/package");
      setDepartments(res?.data || []);
    } catch (err) {
      console.error("Failed to fetch departments:", err);
    }
  };

  // Effects
  useEffect(() => {
    fetchDepartments();
  }, []);

  useEffect(() => {
    const newView = getActiveView();
    if (newView !== activeView) {
      setActiveView(newView);
      setCurrentPage(1);
    }
  }, [searchParams]);

  useEffect(() => {
    if (departments.length > 0) {
      if (activeView === "active") {
        fetchActiveSessionsData();
      } else {
        fetchDepartmentStats();
      }
    }
  }, [departments, activeView, selectedDepartment, startDate, endDate]);

  // Handle Escape key to close modal
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === "Escape" && selectedUser) {
        closeUserSessionsModal();
      }
    };

    document.addEventListener("keydown", handleKeyDown);
    return () => document.removeEventListener("keydown", handleKeyDown);
  }, [selectedUser]);

  // Get current data and pagination
  const currentData = activeView === "aggregated" ? aggregatedStats :
    activeView === "individual" ? individualStats : activeStats;

  const itemsPerPageNum = itemsPerPage === "all" ? currentData.length : parseInt(itemsPerPage, 10);
  const totalPages = itemsPerPageNum === currentData.length ? 1 : Math.ceil(currentData.length / itemsPerPageNum);
  const currentStats = currentData.slice((currentPage - 1) * itemsPerPageNum, currentPage * itemsPerPageNum);

  // Bar chart data formatting
  const formattedBarChartData: BarChartData[] = useMemo(() => {
    return aggregatedStats.map((stat: DepartmentStats) => ({
      name: stat.username,
      download: parseFloat(formatBytesToGB(stat.acctoutputoctets)),
      upload: parseFloat(formatBytesToGB(stat.acctinputoctets)),
    }));
  }, [aggregatedStats]);

  return (
    <div className="p-5 sm:p-5">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center flex-wrap gap-4">
        <h1 className="text-lg sm:text-lg font-bold sm:mb-3 text-wrap">
          Analytics
        </h1>
      </div>
      <div className="grid grid-cols-2 gap-3 mb-3">
        <div className="bg-white p-2 rounded-lg h-[280px] flex items-center justify-center border border-yellow-300">
          <AnalyticsBarChart data={formattedBarChartData} />
        </div>
        <div className="bg-white p-2 rounded-lg h-[280px] flex items-center justify-center border border-green-300">
          <AnalyticsLineChart data={lineChartData} />
        </div>
      </div>
      <div className="grid grid-cols-2 gap-3 mb-3">
        <div className="bg-white p-2 rounded-lg h-[280px] flex items-center justify-center border border-blue-300">
          <div>{/* Placeholder for future chart/content */}</div>
        </div>
        <div className="bg-white p-2 rounded-lg h-[280px] flex items-center justify-center border border-red-300">
          <div>{/* Placeholder for future chart/content */}</div>
        </div>
      </div>

      {/* View Toggle Tabs */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 mb-3">
        <div className="p-3 border-b">
          <div className="flex flex-col sm:flex-row gap-4 w-full">
            <button
              onClick={() => handleViewChange("aggregated")}
              className={`px-4 py-2 rounded-full border w-full sm:w-auto h-10 ${activeView === "aggregated"
                ? "bg-blue-600 text-white"
                : "border-gray-300 text-gray-700 bg-white"
                }`}
            >
              Aggregated View
            </button>
            <button
              onClick={() => handleViewChange("individual")}
              className={`px-4 py-2 rounded-full border w-full sm:w-auto h-10 ${activeView === "individual"
                ? "bg-blue-600 text-white"
                : "border-gray-300 text-gray-700 bg-white"
                }`}
            >
              Individual Sessions
            </button>
            <button
              onClick={() => handleViewChange("active")}
              className={`px-4 py-2 rounded-full border w-full sm:w-auto h-10 ${activeView === "active"
                ? "bg-blue-600 text-white"
                : "border-gray-300 text-gray-700 bg-white"
                }`}
            >
              Active Sessions
            </button>
          </div>
        </div>
      </div>

      {/* Filters - Hidden for Active Sessions */}
      {activeView !== "active" && (
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4 mb-3">
          <div className="flex flex-col lg:flex-row items-start lg:items-center justify-between gap-4 ">
            {/* Left Section - Department Filter and Date Range */}
            <div className="flex flex-col md:flex-row items-start md:items-center gap-4 w-full lg:w-auto">
              <div className="flex flex-col sm:flex-row items-start sm:items-center gap-2 text-xs font-medium text-gray-700 w-full sm:w-auto">
                <span>Department:</span>
                <select
                  value={selectedDepartment}
                  onChange={handleDepartmentChange}
                  className="border border-gray-300 rounded-md px-2 py-1.5 text-xs bg-white shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200 min-w-[180px] w-full sm:w-auto"
                >
                  <option value="">All Departments</option>
                  {departments.map((dept: PackageInfo) => (
                    <option key={dept.id} value={dept.package_name}>
                      {dept.package_name}
                    </option>
                  ))}
                </select>
              </div>

              {/* Date Range Section */}
              <div className="flex flex-col sm:flex-row items-start sm:items-center gap-3 w-full sm:w-auto">
                <span className="text-xs font-medium text-gray-700">
                  Filter by Date:
                </span>
                <div className="flex flex-col sm:flex-row items-center gap-2 w-full sm:w-auto">
                  <div className="flex flex-col w-full sm:w-auto">
                    {/* <label className="text-xs font-medium text-gray-600 mb-1">Start Date</label> */}
                    <input
                      type="date"
                      value={startDate}
                      onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                        setStartDate(e.target.value)
                      }
                      className="border border-gray-300 rounded-md px-2 py-1.5 text-xs bg-white shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200 w-full"
                    />
                  </div>
                  <div className="flex items-center justify-center px-2">
                    <span className="text-gray-400 text-sm">to</span>
                  </div>
                  <div className="flex flex-col w-full sm:w-auto">
                    {/* <label className="text-xs font-medium text-gray-600 mb-1">End Date</label> */}
                    <input
                      type="date"
                      value={endDate}
                      onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                        setEndDate(e.target.value)
                      }
                      className="border border-gray-300 rounded-md px-2 py-1.5 text-xs bg-white shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200 w-full"
                    />
                  </div>
                </div>
              </div>
            </div>

            {/* Right Section - Show Pages and View Info */}
            <div className="flex flex-col lg:flex-row items-end lg:items-center gap-3">
              {/* View Status Indicator */}
              <div className="flex items-center gap-2">
                <div className="text-xs text-gray-600 bg-gray-100 px-3 py-1.5 rounded-md">
                  <span className="font-medium">
                    {activeView === "aggregated"
                      ? "Aggregated"
                      : activeView === "individual"
                        ? "Individual"
                        : "Active"}{" "}
                    View
                  </span>
                  <span className="ml-2 text-blue-600 font-semibold">
                    ({currentData.length} records)
                  </span>
                </div>
              </div>

              {/* Items per page */}
              <div className="flex items-center gap-2 text-xs">
                <span>Show</span>
                <select
                  value={itemsPerPage}
                  onChange={handleItemsPerPageChange}
                  className="border border-gray-300 rounded-md px-2 py-1.5 text-xs focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="15">15</option>
                  <option value="30">30</option>
                  <option value="50">50</option>
                  <option value="100">100</option>
                  <option value="all">All</option>
                </select>
                <span>entries</span>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* View Status for Active Sessions */}
      {activeView === "active" && (
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4 mb-3">
          <div className="flex flex-col lg:flex-row items-start lg:items-center justify-between gap-4">
            <div className="text-xs text-gray-600 bg-gray-100 px-3 py-1.5 rounded-lg">
              <span className="font-medium">Active Sessions</span>
              <span className="ml-2 text-blue-600 font-semibold">
                ({currentData.length} online clients)
              </span>
              <span className="ml-2 text-gray-500">
                {currentData.length > 0 && (currentData[0] as any).created_at
                  ? `(${new Date(
                    (currentData[0] as any).created_at
                  ).toLocaleString()})`
                  : ""}
              </span>
            </div>
          </div>
        </div>
      )}

      <div className="bg-white rounded-lg shadow p-4 overflow-x-auto">
        {loading ? (
          <div className="text-center py-8 text-gray-500">
            Loading analytics data...
          </div>
        ) : currentData?.length === 0 ? (
          <div className="text-center py-8 text-gray-500">
            No analytics data found for the selected criteria.
          </div>
        ) : (
          <div className="w-full overflow-x-auto">
            <table className="min-w-full lg:min-w-max w-full">
              <thead className="bg-gray-200 text-left text-xs uppercase">
                <tr>
                  <th className="px-4 py-2">S.N.</th>
                  <th className="px-4 py-2">Username</th>
                  {(activeView === "individual" || activeView === "active") && (
                    <>
                      <th className="px-4 py-2">Start Time</th>
                      {activeView === "individual" && (
                        <th className="px-4 py-2">Stop Time</th>
                      )}
                      {activeView === "active" && (
                        <th className="px-4 py-2">Duration</th>
                      )}
                    </>
                  )}
                  {activeView !== "active" && (
                    <th className="px-4 py-2">Department</th>
                  )}
                  <th className="px-4 py-2">Upload</th>
                  <th className="px-4 py-2">Download</th>
                  {activeView === "active" && (
                    <>
                      <th className="px-4 py-2">Upload Speed (Mbps)</th>
                      <th className="px-4 py-2">Download Speed (Mbps)</th>
                    </>
                  )}
                </tr>
              </thead>
              <tbody>
                {currentStats.map((item: DepartmentStats, index: number) => (
                  <tr
                    key={`${item.username}-${item.acctstarttime}-${index}`}
                    className="border-b hover:bg-gray-50"
                  >
                    <td className="px-4 py-2 text-xs">
                      {(currentPage - 1) * itemsPerPageNum + index + 1}
                    </td>
                    <td className="px-4 py-2 text-xs">
                      {activeView === "aggregated" ? (
                        <button
                          onClick={() => handleUsernameClick(item.username)}
                          className="text-blue-600 font-medium hover:text-blue-800 hover:underline cursor-pointer"
                        >
                          {item.username}
                        </button>
                      ) : (
                        <span className="text-blue-600 font-medium">
                          {item.username}
                        </span>
                      )}
                    </td>
                    {(activeView === "individual" ||
                      activeView === "active") && (
                        <>
                          <td className="px-4 py-2 text-xs">
                            {new Date(item.acctstarttime).toLocaleString(
                              "en-US",
                              {
                                year: "numeric",
                                month: "2-digit",
                                day: "2-digit",
                                hour: "2-digit",
                                minute: "2-digit",
                                second: "2-digit",
                                hour12: false,
                              }
                            )}
                          </td>
                          {activeView === "individual" && (
                            <td className="px-4 py-2 text-xs">
                              {item.acctstoptime
                                ? new Date(item.acctstoptime).toLocaleString(
                                  "en-US",
                                  {
                                    year: "numeric",
                                    month: "2-digit",
                                    day: "2-digit",
                                    hour: "2-digit",
                                    minute: "2-digit",
                                    second: "2-digit",
                                    hour12: false,
                                  }
                                )
                                : "Active"}
                            </td>
                          )}
                          {activeView === "active" && (
                            <td className="px-4 py-2 text-xs">
                              {(() => {
                                const startTime = new Date(item.acctstarttime);
                                const now = new Date();
                                const durationMinutes = Math.round(
                                  (now.getTime() - startTime.getTime()) /
                                  1000 /
                                  60
                                );
                                return `${durationMinutes} min`;
                              })()}
                            </td>
                          )}
                        </>
                      )}
                    {activeView !== "active" && (
                      <td className="px-4 py-2 text-xs">{item.groupname}</td>
                    )}
                    <td className="px-4 py-2 text-xs">
                      {(Number(item.acctinputoctets) / 1024 / 1024).toFixed(2)}{" "}
                      MB
                    </td>
                    <td className="px-4 py-2 text-xs">
                      {(Number(item.acctoutputoctets) / 1024 / 1024).toFixed(2)}{" "}
                      MB
                    </td>
                    {activeView === "active" && (
                      <>
                        <td className="px-4 py-2 text-xs">
                          {(item as any).inputMbps
                            ? (item as any).inputMbps.toFixed(4)
                            : "0.0000"}
                        </td>
                        <td className="px-4 py-2 text-xs">
                          {(item as any).outputMbps
                            ? (item as any).outputMbps.toFixed(4)
                            : "0.0000"}
                        </td>
                      </>
                    )}
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}

        {/* Pagination controls */}
        {totalPages > 1 && (
          <div className="flex items-center gap-1 justify-left mt-4">
            <Button
              className="rounded-full w-8 h-8 p-0 flex items-center justify-center" /* Adjusted for consistent size */
              size="sm"
              onClick={() => setCurrentPage((p: number) => Math.max(p - 1, 1))}
              disabled={currentPage === 1}
            >
              <ChevronLeft className="h-4 w-4" />{" "}
              {/* Ensure icon size is appropriate */}
            </Button>
            <span className="text-[12px] px-2">
              Page {currentPage} of {totalPages}
            </span>
            <Button
              className="rounded-full w-8 h-8 p-0 flex items-center justify-center" /* Adjusted for consistent size */
              size="sm"
              onClick={() =>
                setCurrentPage((p: number) => Math.min(p + 1, totalPages))
              }
              disabled={currentPage === totalPages}
            >
              <ChevronRight className="h-4 w-4" />{" "}
              {/* Ensure icon size is appropriate */}
            </Button>
          </div>
        )}

        {/* User Sessions Modal */}
        {selectedUser && (
          <div className="fixed inset-0 bg-black bg-opacity-60 flex items-center justify-center z-50 p-4">
            <div className="bg-white rounded-lg p-6 max-h-[90vh] overflow-y-auto w-full max-w-6xl relative">
              <div className="flex justify-between items-center mb-4">
                <h2 className="text-xl font-bold">
                  Individual Sessions for: {selectedUser}
                </h2>
                <button
                  onClick={closeUserSessionsModal}
                  className="text-gray-500 hover:text-gray-700 text-2xl font-bold"
                >
                  ×
                </button>
              </div>

              {/* Search and Controls */}
              <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-4">
                <div className="flex flex-col sm:flex-row items-start sm:items-center gap-3">
                  <div className="text-sm text-gray-600">
                    Total Sessions:{" "}
                    <span className="font-semibold">{userSessions.length}</span>
                  </div>

                  <input
                    type="text"
                    placeholder="Search ..."
                    value={modalSearch}
                    onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
                      setModalSearch(e.target.value);
                      setModalCurrentPage(1);
                    }}
                    className="w-full sm:w-64 px-3 py-1 text-xs border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>

                <div className="flex items-center gap-2 text-xs">
                  <span>Show</span>
                  <select
                    value={modalItemsPerPage}
                    onChange={(e: React.ChangeEvent<HTMLSelectElement>) => {
                      setModalItemsPerPage(Number(e.target.value));
                      setModalCurrentPage(1);
                    }}
                    className="border border-gray-300 rounded px-2 py-1 text-xs focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value={10}>10</option>
                    <option value={25}>25</option>
                    <option value={50}>50</option>
                    <option value={100}>100</option>
                  </select>
                  <span>per page</span>
                </div>
              </div>

              <div className="overflow-x-auto">
                {(() => {
                  // Filter sessions based on search
                  const filteredSessions = userSessions.filter((session: DepartmentStats) => {
                    if (!modalSearch) return true;
                    const searchLower = modalSearch.toLowerCase();
                    return (
                      session.framedipaddress?.toLowerCase().includes(searchLower) ||
                      session.callingstationid?.toLowerCase().includes(searchLower) ||
                      session.acctterminatecause?.toLowerCase().includes(searchLower)
                    );
                  });

                  // Get current page sessions
                  const currentPageSessions = filteredSessions.slice(
                    (modalCurrentPage - 1) * modalItemsPerPage,
                    modalCurrentPage * modalItemsPerPage
                  );

                  return (
                    <>
                      <table className="min-w-full text-xs">
                        <thead className="bg-gray-200 text-left uppercase">
                          <tr>
                            <th className="px-3 py-2">S.N.</th>
                            <th className="px-3 py-2">Start Time</th>
                            <th className="px-3 py-2">Stop Time</th>
                            <th className="px-3 py-2">Duration</th>
                            <th className="px-3 py-2">Department</th>
                            <th className="px-3 py-2">Upload (MB)</th>
                            <th className="px-3 py-2">Download (MB)</th>
                            <th className="px-3 py-2">IP Address</th>
                            <th className="px-3 py-2">MAC Address</th>
                            <th className="px-3 py-2">Terminate Cause</th>
                          </tr>
                        </thead>
                        <tbody>
                          {currentPageSessions.map((session: DepartmentStats, index: number) => {
                            const startTime = new Date(session.acctstarttime);
                            const stopTime = session.acctstoptime
                              ? new Date(session.acctstoptime)
                              : null;
                            const duration = stopTime
                              ? Math.round(
                                (stopTime.getTime() - startTime.getTime()) /
                                1000 /
                                60
                              ) // minutes
                              : null;

                            return (
                              <tr
                                key={`${session.acctstarttime}-${index}`}
                                className="border-b hover:bg-gray-50"
                              >
                                <td className="px-3 py-2">
                                  {(modalCurrentPage - 1) * modalItemsPerPage +
                                    index +
                                    1}
                                </td>
                                <td className="px-3 py-2">
                                  {startTime.toLocaleString("en-US", {
                                    year: "numeric",
                                    month: "2-digit",
                                    day: "2-digit",
                                    hour: "2-digit",
                                    minute: "2-digit",
                                    second: "2-digit",
                                    hour12: false,
                                  })}
                                </td>
                                <td className="px-3 py-2">
                                  {stopTime
                                    ? stopTime.toLocaleString("en-US", {
                                      year: "numeric",
                                      month: "2-digit",
                                      day: "2-digit",
                                      hour: "2-digit",
                                      minute: "2-digit",
                                      second: "2-digit",
                                      hour12: false,
                                    })
                                    : "Active"}
                                </td>
                                <td className="px-3 py-2">
                                  {duration ? `${duration} min` : "Active"}
                                </td>
                                <td className="px-3 py-2">{session.groupname}</td>
                                <td className="px-3 py-2">
                                  {(
                                    Number(session.acctinputoctets) /
                                    1024 /
                                    1024
                                  ).toFixed(2)}
                                </td>
                                <td className="px-3 py-2">
                                  {(
                                    Number(session.acctoutputoctets) /
                                    1024 /
                                    1024
                                  ).toFixed(2)}
                                </td>
                                <td className="px-3 py-2">
                                  {session.framedipaddress}
                                </td>
                                <td className="px-3 py-2">
                                  {session.callingstationid}
                                </td>
                                <td className="px-3 py-2">
                                  {session.acctterminatecause || "Active"}
                                </td>
                              </tr>
                            );
                          })}
                        </tbody>
                      </table>

                      {/* Modal Pagination */}
                      {Math.ceil(filteredSessions.length / modalItemsPerPage) >
                        1 && (
                          <div className="flex items-center justify-between mt-4 pt-4 border-t">
                            <div className="text-xs text-gray-600">
                              Showing{" "}
                              {(modalCurrentPage - 1) * modalItemsPerPage + 1} to{" "}
                              {Math.min(
                                modalCurrentPage * modalItemsPerPage,
                                filteredSessions.length
                              )}{" "}
                              of {filteredSessions.length} sessions
                            </div>

                            <div className="flex items-center gap-1">
                              <Button
                                className="rounded-full w-8 h-8 p-0 flex items-center justify-center"
                                size="sm"
                                onClick={() =>
                                  setModalCurrentPage((p: number) => Math.max(p - 1, 1))
                                }
                                disabled={modalCurrentPage === 1}
                              >
                                <ChevronLeft className="h-4 w-4" />
                              </Button>
                              <span className="text-xs px-2">
                                Page {modalCurrentPage} of{" "}
                                {Math.ceil(
                                  filteredSessions.length / modalItemsPerPage
                                )}
                              </span>
                              <Button
                                className="rounded-full w-8 h-8 p-0 flex items-center justify-center"
                                size="sm"
                                onClick={() =>
                                  setModalCurrentPage((p: number) =>
                                    Math.min(
                                      p + 1,
                                      Math.ceil(
                                        filteredSessions.length / modalItemsPerPage
                                      )
                                    )
                                  )
                                }
                                disabled={
                                  modalCurrentPage ===
                                  Math.ceil(
                                    filteredSessions.length / modalItemsPerPage
                                  )
                                }
                              >
                                <ChevronRight className="h-4 w-4" />
                              </Button>
                            </div>
                          </div>
                        )}
                    </>
                  );
                })()}
              </div>

              <div className="mt-4 flex justify-end">
                <button
                  onClick={closeUserSessionsModal}
                  className="px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600"
                >
                  Close
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}