"use client";
import React from "react";
import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>hart,
  Line,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  <PERSON>lt<PERSON>,
  Legend,
  ResponsiveContainer,
} from "recharts";
import { DepartmentStats } from "@/types/interface-type";

// Define the structure of the data expected by the chart
interface BarChartData {
  name: string;
  download: number;
  upload: number;
}

export interface LineChartData {
  timestamp: string;
  totalUsage: number;
}

interface RawAnalyticsItem {
  created_at: string;
  count: number;
}


interface BarChartProps {
  data: BarChartData[];
}

// The chart component itself
const AnalyticsBarChartComponent: React.FC<BarChartProps> = ({ data }) => {
  if (!data || data.length === 0) {
    return (
      <div className="flex items-center justify-center h-full">
        <p className="text-gray-500">No data to display</p>
      </div>
    );
  }

  const barCount = data.length;
  const dynamicBarSize = Math.max(10, 200 / (barCount || 1));

  return (
    <ResponsiveContainer width="100%" height="100%">
      <BarChart
        data={data}
        layout="vertical"
        margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
      >
        <CartesianGrid strokeDasharray="0 0" />
        <YAxis dataKey="name" type="category" />
        <XAxis type="number" />
        <Tooltip />
        <Legend />
        <Bar
          dataKey="download"
          fill="#8884d8"
          radius={[0, 30, 30, 0]}
          barSize={dynamicBarSize}
        />
        <Bar
          dataKey="upload"
          fill="#82ca9d"
          position="inside"
          barSize={dynamicBarSize}
          radius={[0, 30, 30, 0]}
        />
      </BarChart>
    </ResponsiveContainer>
  );
};

interface LineChartProps {
  data: LineChartData[];
}

const AnalyticsLineChartComponent: React.FC<LineChartProps> = ({ data }) => {
  if (!data || data.length === 0) {
    return <p className="text-gray-500 text-center">No line chart data</p>;
  }

  return (
    <ResponsiveContainer width="100%" height="100%">
      <LineChart
        data={data}
        margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
      >
        <CartesianGrid strokeDasharray="3 3" />
        <XAxis
          dataKey="timestamp"
          tickFormatter={(v) =>
            new Date(v).toLocaleTimeString([], { hour: "2-digit", minute: "2-digit" })
          }
        />
        <YAxis />
        <Tooltip />
        <Legend />
        <Line
          type="monotone"
          dataKey="totalUsage"
          stroke="#ff7300"
          strokeWidth={2}
          dot={{ r: 3 }}
          activeDot={{ r: 5 }}
        />
      </LineChart>
    </ResponsiveContainer>
  );
};

// Memoized version for export
export const AnalyticsBarChart = React.memo(AnalyticsBarChartComponent);
export const AnalyticsLineChart = React.memo(AnalyticsLineChartComponent);


// Function to format raw stats into chart-ready data
export const formatBarChartData = (
  stats: DepartmentStats[]
): BarChartData[] => {
  return stats.map((stat) => ({
    name: stat.username,
    download: parseFloat(
      (Number(stat.acctoutputoctets) / (1024 * 1024 * 1024)).toFixed(2)
    ), // GB
    upload: parseFloat(
      (Number(stat.acctinputoctets) / (1024 * 1024 * 1024)).toFixed(2)
    ), // GB
  }));
};

export const formatLineChartData = (
  rawData: RawAnalyticsItem[]
): LineChartData[] => {
  return rawData.map((item) => ({
    timestamp: item.created_at,
    totalUsage: item.count,
  }));
};

// Function to get the top N items from the chart data
export const getTopN = (data: BarChartData[], count: number): BarChartData[] => {
  return [...data].sort((a, b) => b.download - a.download).slice(0, count);
};

