"use Client";
import { useState } from "react";

interface deleteProps {
  id: number;
  paraValue: string;
  value: string;
  onDelete: (id: number) => void | Promise<void>;
  onClose: () => void;
  loading: boolean;
}
export default function deleteConfirm({
  id,
  paraValue,
  value,
  onDelete,
  onClose,
  loading,
}: deleteProps) {
  const [inputName, setInputName] = useState("");
  const [error,setError]=useState("");
  const handleClick=()=>{
    if(inputName.toLowerCase() === value.toLowerCase()){
      onDelete(id);
      onClose();
    }
    else{
      setError(" Name should match exactly !!! ")
    }
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-60 flex items-center justify-center">
      <div className="bg-white p-7 rounded-lg w-auto max-w-sm shadow-lg">
        <h2 className="text-xl font-bold mb-4 text-center">Confirm Deletion</h2>
        <p className="mb-2 text-left">
          Are you sure you want to delete the {paraValue} member{" "}
          <span>{value}</span>?
        </p>
        <p className="mb-4 text-sm text-gray-600 text-left">
          Type the {paraValue} name to confirm:
        </p>
        <input
          className="border px-3 py-2 w-full mb-4 rounded"
          value={inputName}
          onChange={(e) => setInputName(e.target.value)}
          placeholder={`Enter ${paraValue} name`}
          autoFocus
        />
        {error && <p className="text-red-500 text-sm">{error}</p>}
        <div className="flex justify-end gap-2">
          <button
            onClick={onClose}
            className="px-4 py-2 bg-gray-300 rounded"
          >
            Cancel
          </button>
          <button
            onClick={handleClick}
            className="px-4 py-2 bg-red-600 text-white rounded"
            disabled={loading}
          >
            {loading ? "Deleting..." : "Confirm Delete"}
          </button>
        </div>
      </div>
    </div>
  );
}
