"use client";
import Image from "next/image";
import { useState, useEffect, React } from "react";
import { useRouter } from "next/navigation";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Eye, EyeOff, Loader2 } from "@/components/icons/list";
import { useAuth } from "@/context/AuthContext";

export default function UserLogin() {
  const { login, isLoggedIn, isAuthReady } = useAuth();
  const router = useRouter();
  const [form, setForm] = useState({ username: "", password: "" });
  const [showPassword, setShowPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [errorMessage, setErrorMessage] = useState("");

  useEffect(() => {
    if (isAuthReady && isLoggedIn) {
      router.replace("/app");
    }
  }, [isAuthReady, isLoggedIn, router]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (isLoading) return;

    setErrorMessage("");
    setIsLoading(true);

    try {
      await login(form.username, form.password);
    } catch (err: any) {
      setErrorMessage(
        err.response?.data?.message ||
          err.message ||
          "An unexpected error occurred"
      );
      setForm((prev) => ({ ...prev, password: "" }));
    } finally {
      setIsLoading(false);
    }
  };

  if (isAuthReady && isLoggedIn) return null;

  return (
    <div className="flex items-center justify-center min-h-screen bg-blue-900 w-full">
      <div className="bg-white p-6 rounded-lg shadow-md w-full max-w-sm">
        <div className="flex justify-center mb-6">
          <Image
            src="/images/nepalpolice.jpg"
            alt="nepal police logo"
            width={150}
            height={150}
            priority
          />
        </div>
        <h2 className="text-xl font-semibold mb-4 text-center">
          Login to Your Account
        </h2>
        <form onSubmit={handleSubmit} className="space-y-4">
          <Input
            placeholder="Username"
            value={form.username}
            onChange={(e) => {
              setForm((prev) => ({ ...prev, username: e.target.value }));
              setErrorMessage("");
            }}
            className={`border border-gray-400 rounded-full ${
              errorMessage ? "border-red-500" : ""
            }`}
          />

          <div className="relative ">
            <Input
              placeholder="Password"
              type={showPassword ? "text" : "password"}
              value={form.password}
              onChange={(e) =>
                setForm((prev) => ({ ...prev, password: e.target.value }))
              }
              className={`border border-gray-400 rounded-full ${
                errorMessage ? "border-red-500" : ""
              }`}
            />
            <button
              type="button"
              onClick={() => setShowPassword((prev) => !prev)}
              className="absolute right-2 top-1/2 -translate-y-1/2 text-gray-500 "
              disabled={isLoading}
            >
              {showPassword ? <EyeOff /> : <Eye />}
            </button>
          </div>

          {errorMessage && (
            <p className="text-red-500 text-sm text-center">{errorMessage}</p>
          )}

          <Button
            className="w-full rounded-full"
            type="submit"
            disabled={!form.username || !form.password || isLoading}
          >
            {isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            {isLoading ? "Logging in..." : "Login"}
          </Button>

          <div className="text-center mt-4">
            <a
              href="#"
              onClick={(e) => {
                e.preventDefault();
                alert(
                  "Please Contact the System Administrator for Password Reset."
                );
              }}
              className="text-gray-900 hover:underline text-sm"
            >
              Forgot Password ?
            </a>
          </div>
          <div className="flex justify-center">
            <h1 className="text-center text-sm text-gray-500 mt-5">
              © 2025{" "}
              <a
                href="https://workalaya.com/"
                target="_blank"
                rel="noopener noreferrer"
                className="hover:text-red-600 transition-colors duration-200 text-red-500"
              >
                Workalaya
              </a>
              . All rights reserved.
            </h1>
          </div>
        </form>
      </div>
    </div>
  );
}
