"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
// import { useRouter } from "next/navigation";
import apiClient from "@/lib/apiClient";
import { Plus } from "@/components/icons/list";
import { NAS, NASFormData } from "@/types/interface-type";
import { toast } from "sonner";
import { SquarePen, Trash2, Eye, EyeOff } from "@/components/icons/list";
import DeleteConfirm from "@/components/delete-dailog";
import NASForm from "@/components/settings/devices/devices-form";
import { useAuth } from "@/context/AuthContext";
import { useFetch } from "@/hooks/useFetchOnMount";
import { Input } from "@/components/ui/input";

const NasPage = () => {
  // const router = useRouter();
  // const [nasData, setNasData] = useState<NAS[]>([]);
  const [selectedNas, setSelectedNas] = useState(null);
  const [showSecretMap, setShowSecretMap] = useState<{
    [key: string]: boolean;
  }>({});
  const { setIsRefreshed } = useAuth();
  const [isAddOpen, setIsAddOpen] = useState(false);
  const [isEditOpen, setIsEditOpen] = useState(false);
  const [search, setSearch] = useState("");
  const { data: nas, loading: nasLoading, error: nasError } = useFetch("/nas");
  let nasData: NAS[] = nas || [];
  const [isDelete, setDeleteOpen] = useState(false);

  const filteredDevices = nasData?.filter(
    (group) =>
      group?.nasname?.toLowerCase().includes(search.toLowerCase()) ||
      group?.name?.toLowerCase().includes(search.toLowerCase()) ||
      group?.vendor?.toLowerCase().includes(search.toLowerCase())
  );

  const handleEditSubmit = async (updatedNAS: NASFormData) => {
    const { id, ...payload } = updatedNAS || {};
    try {
      const response = await apiClient.patch(`/nas/${id}`, payload);
      toast.success("Device has been successfully updated!!");
      // console.log("NAS updated:", response.data);
      setIsEditOpen(false);
      setIsRefreshed((prev) => !prev);
      // sessionStorage.removeItem("editNAS");
      // router.push("/app/nas");
    } catch (error) {
      console.error("Failed to update device:", error);
      toast.error("Failed to update Device. Please try again.");
    }
  };

  const handleSubmit = async (nasData: NASFormData) => {
    try {
      const response = await apiClient.post("/nas", nasData);
      toast.success("Device created sucessfully");
      // console.log("NAS created:", response.data);
      setIsAddOpen(false);
      setIsRefreshed((prev) => !prev);

      // router.push("/app/nas");
    } catch (error) {
      console.error("Failed to create device:", error);
      alert("Failed to create device. Please try again.");
    }
  };
  const handleDeleteClick = (nas) => {
    setSelectedNas(nas);
    setDeleteOpen(true);
  };
  const handleDeleteNAS = async (id: number) => {
    try {
      await apiClient.delete(`/nas/${id}`);
      toast.success("Device deleted sucessfully");
      setIsRefreshed((prev) => !prev);
      setShowSecretMap((prevMap) => {
        const newMap = { ...prevMap };
        delete newMap[id];
        return newMap;
      });
    } catch (error) {
      console.error("Failed to delete device:", error);
      toast.error("Failed to delete device. Please try again.");
    }
  };

  const handleEditNAS = (nas: NAS) => {
    setSelectedNas(nas);
    setIsEditOpen(true);
  };

  const toggleSecretVisibility = (id: string) => {
    setShowSecretMap((prevMap) => ({
      ...prevMap,
      [id]: !prevMap[id],
    }));
  };

  return (
    <div className="p-5 sm:p-5 space-y-3">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center flex-wrap gap-4">
        <h1 className="text-xl sm:text-xl font-bold sm:mb-3 text-wrap">
          Device Management
        </h1>
        <div className="flex flex-col sm:flex-row items-stretch sm:items-center gap-3 w-full sm:w-auto">
          <Input
            type="text"
            placeholder="Search by IP, Name or Vendor"
            value={search}
            onChange={(e) => setSearch(e.target.value)}
            className="w-full sm:w-[250px] p-1 border border-gray-300 rounded-full px-3 focus:ring focus:border-blue-300"
          />
          <Button
            className="w-full sm:w-auto px-3 rounded-full"
            onClick={() => setIsAddOpen(true)}
          >
            Add New Device
          </Button>
        </div>
      </div>
      <div className="bg-white rounded-lg shadow p-4 overflow-x-auto">
        <div className="w-full overflow-x-auto rounded ">
          <table className="min-w-max w-full">
            <thead className="bg-gray-200 text-left text-sm uppercase">
              <tr>
                <th className="px-2 py-2 text-left text-base sm:text-sm whitespace-nowrap">
                  SN
                </th>
                <th className="px-2 py-2 text-left text-base sm:text-sm whitespace-nowrap">
                  Device IP
                </th>
                <th className="px-2 py-2 text-left text-base sm:text-sm whitespace-nowrap">
                  Name
                </th>
                <th className="px-2 py-2 text-left text-base sm:text-sm whitespace-nowrap">
                  Description
                </th>
                <th className="px-2 py-2 text-left text-base sm:text-sm whitespace-nowrap">
                  Vendor
                </th>
                <th className="px-2 py-2 text-left text-base sm:text-sm whitespace-nowrap">
                  Secret
                </th>
                <th className="px-2 py-2 text-left text-base sm:text-sm whitespace-nowrap">
                  Action
                </th>
              </tr>
            </thead>
            <tbody>
              {filteredDevices && filteredDevices?.length > 0 ? (
                filteredDevices.map((item: NAS, idx: number) => (
                  <tr
                    key={item?.id || idx}
                    className="border-b hover:bg-gray-50 "
                  >
                    <td className="px-2 py-2 text-sm">{idx + 1}</td>
                    <td className="px-2 py-2 text-sm whitespace-nowrap">
                      {item?.nasname}
                    </td>
                    <td className="px-2 py-2 text-sm whitespace-nowrap">
                      {item?.name}
                    </td>
                    <td className="px-2 py-2 text-sm whitespace-nowrap">
                      {item?.description}
                    </td>
                    <td className="px-2 py-2 text-sm whitespace-nowrap">
                      {item?.vendor}
                    </td>
                    <td className="px-2 py-2 whitespace-nowrap text-gray-900">
                      {item?.secret ? (
                        <div className="flex items-center font-mono w-25 space-x-2">
                          <span className="truncate inline-block max-w-[5.5rem]">
                            {showSecretMap[item?.id!]
                              ? item?.secret
                              : "***********"}
                          </span>
                          <button
                            onClick={() => toggleSecretVisibility(item?.id!)}
                            className="text-gray-500 hover:text-gray-700 focus:outline-none h-6 w-6"
                            aria-label={
                              showSecretMap[item?.id!]
                                ? "Hide secret"
                                : "Show secret"
                            }
                            aria-pressed={showSecretMap[item?.id!]}
                          >
                            {showSecretMap[item?.id!] ? <EyeOff /> : <Eye />}
                          </button>
                        </div>
                      ) : (
                        "N/A"
                      )}
                    </td>
                    <td className="px-3 py-2 whitespace-nowrap w-20">
                      <div className="flex flex-col sm:flex-row items-center justify-start sm:gap-2">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleEditNAS(item)}
                          className="bg-green-400 hover:bg-green-500 text-white p-2 rounded-md h-6 w-6"
                        >
                          <SquarePen />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleDeleteClick(item)}
                          className="bg-red-500 hover:bg-red-600 text-white p-2 rounded-md h-6 w-6"
                        >
                          <Trash2 />
                        </Button>
                      </div>
                    </td>
                  </tr>
                ))
              ) : (
                <tr>
                  <td
                    colSpan={5}
                    className="text-center py-6 text-gray-500 text-sm"
                  >
                    No devices found.
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
        {isAddOpen && (
          <NASForm
            onSubmit={handleSubmit}
            onCancel={() => setIsAddOpen(false)}
            isEdit={false}
          />
        )}
        {isEditOpen && (
          <NASForm
            initialData={selectedNas}
            onSubmit={handleEditSubmit}
            onCancel={() => setIsEditOpen(false)}
            isEdit={true}
          />
        )}
      </div>
      {isDelete && (
        <DeleteConfirm
          id={selectedNas?.id}
          paraValue="NAS"
          value={selectedNas?.name}
          onDelete={handleDeleteNAS}
          onClose={() => setDeleteOpen(false)}
          loading={false}
        ></DeleteConfirm>
      )}
    </div>
  );
};

export default NasPage;
