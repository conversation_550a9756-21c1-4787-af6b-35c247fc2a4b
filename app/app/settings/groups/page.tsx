"use client";

import { useState, useEffect } from "react";
import { Input } from "@/components/ui/input"; // Import Input for the search bar

import {
  GroupTable,
  group,
} from "@/components/settings/groups/group-management";
import { <PERSON><PERSON> } from "@/components/ui/button";
import apiClient from "@/lib/apiClient";
import Cookies from "js-cookie";
import { Trash2 } from "@/components/icons/list";
import { AddGroupForm } from "@/components/settings/groups/group-management";
import { EditGroupForm } from "@/components/settings/groups/edit-group";
import { toast } from "sonner";
import { useAuth } from "@/context/AuthContext";

export default function GroupManagementPage() {
  const [groups, setGroups] = useState<group[]>();
  const [search, setSearch] = useState("");
  const accessToken = Cookies.get("accessToken");
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);
  const [isEditOpen, setIsEditOpen] = useState(false);
  const [selectedGroup, setSelectedGroup] = useState<group>();
  const { isRefreshed, setIsRefreshed } = useAuth();

  const filteredGroups = groups?.filter((group) =>
    group?.name?.toLowerCase().includes(search.toLowerCase())
  );

  useEffect(() => {
    const fetchGroups = async () => {
      try {
        const response = await apiClient.get("/groups");
        // Backend returns: { success: true, message: "List Groups", data: [...] }
        // The apiClient response interceptor extracts the data property
        setGroups(response?.data || []);
      } catch (error) {
        console.error("Failed to fetch groups:", error);
        toast.error(error.message || "Failed to fetch groups");
      }
    };

    fetchGroups();
  }, [accessToken, isRefreshed]);

  const handleDeleteGroup = async (name: string) => {
    try {
      await apiClient.delete(`/group/${name}`, {
        headers: {
          Authorization: `Bearer ${accessToken}`,
        },
      });
      // Backend returns: { success: true, message: "Group has been deleted" }
      setGroups((prev) => prev?.filter((group) => group?.name !== name));
      toast.success("Group deleted successfully");
    } catch (error) {
      console.error("Failed to delete group:", error);
      toast.error(error.message || "Failed to delete group. Please try again.");
    }
  };
  const handleEdit = (group) => {
    setIsEditOpen(true);
    setSelectedGroup(group);
  };
  const handleEditSubmit = async (updatedGroup: group) => {
    const new_group_name = updatedGroup?.new_group_name;
    try {
      const response = await apiClient.patch(`/group/${updatedGroup?.name}`, {
        new_group_name: new_group_name,
      });
      // Backend returns: { success: true, message: "Group has been updated" }
      toast.success("Group Name Edit Successfull");
      setInterval(setIsEditOpen(false), 1000);
      setIsRefreshed((prev) => !prev);
    } catch (error) {
      console.error("Failed to update group:", error);
      toast.error(error.message || "Failed to update group");
    }
  };
  const handleSubmit = async (groupname: group) => {
    try {
      const response = await apiClient.post("/group", groupname);
      // Backend returns: { success: true, message: "Created Group" }
      toast.success("Group Added successfully");
      setInterval(setIsAddModalOpen(false), 1000);
      setIsRefreshed((prev) => !prev);
    } catch (error) {
      console.error("Failed to add group:", error);
      toast.error(error.message || "Failed to add group");
    }
  };

  return (
    <div className="p-5 sm:p-5 space-y-3">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center flex-wrap gap-4">
        <h1 className="text-xl sm:text-xl font-bold sm:mb-3 text-wrap">
          Group Management{" "}
        </h1>
        <div className="flex flex-col sm:flex-row items-stretch sm:items-center gap-3 w-full sm:w-auto">
          <Input
            type="text"
            placeholder="Search Groups"
            value={search}
            onChange={(e) => setSearch(e.target.value)}
            className="w-full sm:w-[250px] p-1 border border-gray-300 rounded-full px-3 focus:ring focus:border-blue-300"
          />
          <Button
            className="w-full sm:w-auto px-3 rounded-full"
            onClick={() => setIsAddModalOpen(true)}
          >
            Add New Group
          </Button>
        </div>
      </div>

      <GroupTable
        groups={filteredGroups}
        onDelete={handleDeleteGroup}
        handleEdit={handleEdit}
      />
      {isAddModalOpen && (
        <AddGroupForm
          onSubmit={handleSubmit}
          onClose={() => setIsAddModalOpen(false)}
        />
      )}
      {isEditOpen && (
        <EditGroupForm
          onClose={() => setIsEditOpen(false)}
          group={selectedGroup}
          onSubmit={handleEditSubmit}
        />
      )}
    </div>
  );
}

export function DeleteGroupDialog({ name, onDelete }) {

  const [inputName, setInputName] = useState("");

  const handleDelete = () => {
    if (inputName === name) {
      onDelete(name);
      setShowConfirm(false);
    } else {
      alert("Group name does not match");
    }
  };
  const [showConfirm, setShowConfirm] = useState(false);
  useEffect(() => {
    const handleKeyDown = (e) => {
      if (e.key === "Escape") {
        setShowConfirm(false);
        setInputName("");
      }
    };

    if (showConfirm) {
      document.addEventListener("keydown", handleKeyDown);
    }

    return () => {
      document.removeEventListener("keydown", handleKeyDown);
    };
  }, [showConfirm]);

  return (
    <>
      <Button
        onClick={() => setShowConfirm(true)}
        className="bg-red-500 hover:bg-red-600 text-white p-1 rounded-md h-7 w-7"
      >
        <Trash2 className="h-4 w-4" />
      </Button>

      {showConfirm && (
        <div className="fixed inset-0 bg-black bg-opacity-60 flex items-center justify-center">
          <div className="bg-white p-7 rounded-lg w-auto">
            <h2 className="text-xl font-bold mb-4 text-center">
              Confirm Deletion
            </h2>
            <p className="mb-2 text-left">
              Are you sure you want to delete the group <strong>{name}</strong>?
            </p>
            <p className="mb-4 text-sm text-gray-600 text-left">
              Type the group name to confirm:
            </p>
            <input
              className="border px-3 py-2 w-full mb-4"
              value={inputName}
              onChange={(e) => setInputName(e.target.value)}
              placeholder="Enter group name"
            />
            <div className="flex justify-end gap-2">
              <button
                onClick={() => setShowConfirm(false)}
                className="px-4 py-2 bg-gray-300 rounded"
              >
                Cancel
              </button>
              <button
                onClick={handleDelete}
                className="px-4 py-2 bg-red-600 text-white rounded"
              >
                Confirm Delete
              </button>
            </div>
          </div>
        </div>
      )}
    </>
  );
}
